import { useRef, useState, useEffect } from 'react';
import Head from 'next/head';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getCabinetsData, getFooterData } from '../database/api-client.js';
import { getImageURL } from '../src/config/api.js';
import Navbar from '../src/components/Navbar';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const CabinetsPage = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [cabinets, setCabinets] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [socialMedia, setSocialMedia] = useState([]);

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadCabinetsData = async () => {
      try {
        const cabinetsData = await getCabinetsData();
        console.log('Loaded cabinets data:', cabinetsData);
        if (cabinetsData && cabinetsData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedCabinets = cabinetsData.map(cabinet => ({
            id: cabinet.id,
            title: cabinet.title,
            description: cabinet.description,
            category: cabinet.category_slug || 'wardrobe-cabinets',
            images: cabinet.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          console.log('Formatted cabinets:', formattedCabinets);
          setCabinets(formattedCabinets);
        } else {
          console.log('No cabinets data found, using fallback data');
          // استخدام بيانات احتياطية إذا لم تكن هناك بيانات في قاعدة البيانات
          setCabinets(fallbackCabinets);
        }
      } catch (error) {
        console.error('Error loading cabinets data:', error);
        // استخدام بيانات احتياطية في حالة الخطأ
        setCabinets(fallbackCabinets);
      }
    };

    const loadFooterData = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData) {
          setWhatsappNumber(footerData.whatsapp_number || '966501234567');
          setSocialMedia(footerData.social_media || []);
        }
      } catch (error) {
        console.error('Error loading footer data:', error);
        setWhatsappNumber('966501234567');
        setSocialMedia([]);
      }
    };

    loadCabinetsData();
    loadFooterData();
  }, []);

  // بيانات احتياطية للخزائن
  const fallbackCabinets = [
    {
      id: 1,
      title: 'خزانة ملابس عصرية',
      description: 'تصميم خزانة ملابس عصرية بأحدث التقنيات والمواد عالية الجودة',
      category: 'wardrobe-cabinets',
      images: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
      ]
    },
    // يمكن إضافة المزيد من البيانات الاحتياطية هنا
  ];

  return (
    <>
      <Head>
        <title>معرض الخزائن العصرية والكلاسيكية - عجائب الخبراء</title>
        <meta name="description" content="استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس والمكاتب. أحدث تصاميم الخزائن في السعودية مع عجائب الخبراء" />
        <meta name="keywords" content="خزائن ملابس, خزائن عصرية, خزائن كلاسيكية, خزائن مكاتب, تصاميم خزائن, معرض خزائن, عجائب الخبراء" />
        <link rel="canonical" href="https://khobrakitchens.com/cabinets" />
        <meta property="og:title" content="معرض الخزائن العصرية والكلاسيكية - عجائب الخبراء" />
        <meta property="og:description" content="استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس والمكاتب." />
        <meta property="og:url" content="https://khobrakitchens.com/cabinets" />
        <meta property="og:type" content="website" />
      </Head>

      <div className="bg-gray-50">
        <Navbar />
        {/* سيتم إضافة باقي المحتوى هنا */}
        <div className="min-h-screen pt-20">
          <div className="container mx-auto px-4 py-8">
            <h1 className="text-4xl font-bold text-center mb-8">معرض الخزائن</h1>
            <p className="text-center text-gray-600 mb-12">
              استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية
            </p>
            
            {/* معرض الخزائن مع إمكانية فتح الموديل */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {cabinets.map((cabinet) => (
                <div
                  key={cabinet.id}
                  className="bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  onClick={() => {
                    setLightboxImage(cabinet);
                    setModalImageIndex(0);
                  }}
                >
                  {cabinet.images && cabinet.images.length > 0 && (
                    <img
                      src={cabinet.images[0]}
                      alt={cabinet.title}
                      className="w-full h-64 object-cover"
                    />
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2">{cabinet.title}</h3>
                    <p className="text-gray-600 mb-4">{cabinet.description}</p>
                    <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* موديل عرض تفاصيل الخزانة */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
            onClick={() => setLightboxImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex flex-col md:flex-row">
                {/* قسم الصور */}
                <div className="md:w-2/3">
                  {lightboxImage.images && lightboxImage.images.length > 0 && (
                    <div className="relative">
                      <Swiper
                        modules={[Navigation, Pagination, Thumbs]}
                        navigation
                        pagination={{ clickable: true }}
                        thumbs={{ swiper: thumbsSwiper }}
                        className="h-96 md:h-[500px]"
                      >
                        {lightboxImage.images.map((image, index) => (
                          <SwiperSlide key={index}>
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </SwiperSlide>
                        ))}
                      </Swiper>

                      {/* صور مصغرة */}
                      {lightboxImage.images.length > 1 && (
                        <Swiper
                          modules={[Thumbs]}
                          onSwiper={setThumbsSwiper}
                          spaceBetween={10}
                          slidesPerView={4}
                          watchSlidesProgress
                          className="mt-2 h-20"
                        >
                          {lightboxImage.images.map((image, index) => (
                            <SwiperSlide key={index}>
                              <img
                                src={image}
                                alt={`صورة مصغرة ${index + 1}`}
                                className="w-full h-full object-cover rounded cursor-pointer"
                              />
                            </SwiperSlide>
                          ))}
                        </Swiper>
                      )}
                    </div>
                  )}
                </div>

                {/* قسم التفاصيل */}
                <div className="md:w-1/3 p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h2 className="text-2xl font-bold text-gray-800">{lightboxImage.title}</h2>
                    <button
                      onClick={() => setLightboxImage(null)}
                      className="text-gray-500 hover:text-gray-700 text-2xl"
                    >
                      ×
                    </button>
                  </div>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {lightboxImage.description}
                  </p>

                  {/* أزرار التواصل */}
                  <div className="space-y-3">
                    <a
                      href={`https://wa.me/${whatsappNumber}?text=مرحباً، أريد الاستفسار عن ${lightboxImage.title}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
                    >
                      <i className="ri-whatsapp-line text-xl"></i>
                      تواصل عبر واتساب
                    </a>

                    <button
                      onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                      className="w-full bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
                    >
                      <i className="ri-phone-line text-xl"></i>
                      طلب عرض سعر
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
};

export default CabinetsPage;
