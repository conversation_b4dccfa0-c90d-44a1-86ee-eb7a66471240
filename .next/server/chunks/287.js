exports.id=287,exports.ids=[287],exports.modules={1161:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(8732),e=c(2015),f=c(9918),g=c.n(f),h=c(4233),i=c(6745);let j=()=>{let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(!1),j=(0,h.useRouter)(),[k,l]=(0,e.useState)([]);(0,e.useEffect)(()=>{let a=async()=>{try{let a=await (0,i.wf)();a&&a.socialMedia&&a.socialMedia.length>0?l(a.socialMedia):l([])}catch(a){console.error("Error loading footer data:",a),l([])}};a();let b=setInterval(a,3e4);return()=>{clearInterval(b)}},[]),(0,e.useEffect)(()=>{let a=()=>{b(window.scrollY>50)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]);let m=()=>{f(!1)};return(0,d.jsx)("nav",{className:`fixed w-full z-50 transition-all duration-500 ${a?"bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-lg shadow-2xl border-b border-white/10":"glass-nav"}`,children:(0,d.jsxs)("div",{className:"container mx-auto px-6 py-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"group cursor-pointer",children:[(0,d.jsx)("div",{className:`font-['Pacifico'] text-3xl bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent hover:scale-110 transition-all duration-300 drop-shadow-lg ${a?"":"drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]"}`,children:"عجائب الخبراء"}),(0,d.jsx)("div",{className:"h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"})]}),(0,d.jsx)("div",{className:"hidden lg:flex items-center space-x-8 rtl:space-x-reverse",children:[{href:"/",text:"الرئيسية",icon:"ri-home-4-line",isRoute:!0},{href:"/kitchens",text:"المطابخ",icon:"ri-restaurant-line",isRoute:!0},{href:"/cabinets",text:"الخزائن",icon:"ri-archive-line",isRoute:!0},{href:"#why-us",text:"لماذا نحن",icon:"ri-star-line",isRoute:!1},{href:"#contact",text:"تواصل معنا",icon:"ri-phone-line",isRoute:!1}].map((b,c)=>{let e=j.pathname===b.href;return b.isRoute?(0,d.jsxs)(g(),{href:b.href,className:`group relative px-4 py-2 font-medium transition-all duration-300 rounded-lg ${e?"text-transparent bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text":a?"text-white hover:text-transparent hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-500 hover:bg-clip-text":"text-gray-800 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-500 hover:bg-clip-text"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,d.jsx)("i",{className:`${b.icon} text-sm ${e?"opacity-100":"opacity-0 group-hover:opacity-100"} transition-opacity duration-300`}),(0,d.jsx)("span",{children:b.text})]}),(0,d.jsx)("div",{className:`absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 transition-all duration-300 ${e?"w-full":"w-0 group-hover:w-full"}`}),(0,d.jsx)("div",{className:"absolute inset-0 bg-white/5 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-10"})]},c):(0,d.jsxs)("a",{href:b.href,className:`group relative px-4 py-2 font-medium transition-all duration-300 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-500 hover:bg-clip-text rounded-lg ${a?"text-white":"text-gray-800"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,d.jsx)("i",{className:`${b.icon} text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300`}),(0,d.jsx)("span",{children:b.text})]}),(0,d.jsx)("div",{className:"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-white/5 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-10"})]},c)})}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-3 rtl:space-x-reverse",children:k.length>0&&k.map((b,c)=>{let{gradient:e,hoverColor:f}={instagram:{gradient:"from-purple-500 to-pink-500",hoverColor:"hover:shadow-purple-500/50"},tiktok:{gradient:"from-gray-800 to-black",hoverColor:"hover:shadow-gray-800/50"},snapchat:{gradient:"from-yellow-400 to-yellow-500",hoverColor:"hover:shadow-yellow-400/50"},whatsapp:{gradient:"from-green-500 to-green-600",hoverColor:"hover:shadow-green-500/50"},twitter:{gradient:"from-blue-400 to-blue-600",hoverColor:"hover:shadow-blue-500/50"}}[b.platform]||{gradient:"from-gray-500 to-gray-600",hoverColor:"hover:shadow-gray-500/50"};return(0,d.jsxs)("a",{href:b.url,className:`relative w-11 h-11 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center hover:scale-110 transition-all duration-300 group border border-white/20 ${f} hover:shadow-lg`,title:b.platform,children:[(0,d.jsx)("i",{className:`${b.icon} text-lg group-hover:scale-110 transition-transform duration-300 ${a?"text-white":"text-gray-700"}`}),(0,d.jsx)("div",{className:`absolute inset-0 bg-gradient-to-r ${e} rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`}),(0,d.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-xl scale-0 group-hover:scale-100 transition-transform duration-300 -z-20"})]},c)})}),(0,d.jsxs)("div",{className:`lg:hidden relative w-12 h-12 flex items-center justify-center cursor-pointer bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 group ${a?"text-white":"text-gray-800"}`,onClick:()=>{f(!c)},children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("i",{className:`ri-menu-line text-xl transition-all duration-300 ${c?"rotate-90 opacity-0":"rotate-0 opacity-100"}`}),(0,d.jsx)("i",{className:`ri-close-line text-xl absolute inset-0 transition-all duration-300 ${c?"rotate-0 opacity-100":"-rotate-90 opacity-0"}`})]}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"})]})]}),(0,d.jsx)("div",{className:`lg:hidden mt-6 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl absolute left-4 right-4 top-20 border border-white/30 transition-all duration-500 overflow-hidden ${c?"opacity-100 translate-y-0 scale-100":"opacity-0 -translate-y-4 scale-95 pointer-events-none"}`,children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"flex flex-col space-y-2",children:[{href:"/",text:"الرئيسية",icon:"ri-home-4-line",color:"from-blue-500 to-blue-600",isRoute:!0},{href:"#why-us",text:"لماذا نحن",icon:"ri-star-line",color:"from-purple-500 to-purple-600",isRoute:!1},{href:"/kitchens",text:"المطابخ",icon:"ri-restaurant-line",color:"from-green-500 to-green-600",isRoute:!0},{href:"/cabinets",text:"الخزائن",icon:"ri-archive-line",color:"from-orange-500 to-orange-600",isRoute:!0},{href:"#contact",text:"تواصل معنا",icon:"ri-phone-line",color:"from-pink-500 to-pink-600",isRoute:!1}].map((a,b)=>{let c=j.pathname===a.href;return a.isRoute?(0,d.jsxs)(g(),{href:a.href,className:`group relative font-medium py-4 px-4 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 border border-transparent hover:border-gray-200 ${c?"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200":"text-gray-800"}`,onClick:m,style:{animationDelay:`${100*b}ms`},children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,d.jsx)("div",{className:`w-10 h-10 rounded-lg bg-gradient-to-r ${a.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 ${c?"scale-110":""}`,children:(0,d.jsx)("i",{className:`${a.icon} text-white text-lg`})}),(0,d.jsx)("span",{className:`text-lg group-hover:text-gray-900 transition-colors duration-300 ${c?"text-gray-900 font-semibold":""}`,children:a.text})]}),(0,d.jsx)("div",{className:`absolute left-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 ${c?"w-full":"w-0 group-hover:w-full"}`})]},b):(0,d.jsxs)("a",{href:a.href,className:"group relative text-gray-800 font-medium py-4 px-4 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 border border-transparent hover:border-gray-200",onClick:m,style:{animationDelay:`${100*b}ms`},children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,d.jsx)("div",{className:`w-10 h-10 rounded-lg bg-gradient-to-r ${a.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`,children:(0,d.jsx)("i",{className:`${a.icon} text-white text-lg`})}),(0,d.jsx)("span",{className:"text-lg group-hover:text-gray-900 transition-colors duration-300",children:a.text})]}),(0,d.jsx)("div",{className:"absolute left-0 bottom-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300"})]},b)})}),(0,d.jsxs)("div",{className:"border-t border-gray-200/50 pt-6 mt-6",children:[(0,d.jsxs)("div",{className:"text-center mb-4",children:[(0,d.jsx)("p",{className:"text-gray-600 text-sm font-medium mb-2",children:"تابعنا على"}),(0,d.jsx)("div",{className:"w-16 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"})]}),k.length>0&&(0,d.jsx)("div",{className:"flex justify-center space-x-4 rtl:space-x-reverse",children:k.map((a,b)=>{let{gradient:c,shadow:e}={instagram:{gradient:"from-purple-500 to-pink-500",shadow:"shadow-purple-500/30"},tiktok:{gradient:"from-gray-800 to-black",shadow:"shadow-gray-800/30"},snapchat:{gradient:"from-yellow-400 to-yellow-500",shadow:"shadow-yellow-400/30"},whatsapp:{gradient:"from-green-500 to-green-600",shadow:"shadow-green-500/30"},twitter:{gradient:"from-blue-400 to-blue-600",shadow:"shadow-blue-500/30"}}[a.platform]||{gradient:"from-gray-500 to-gray-600",shadow:"shadow-gray-500/30"};return(0,d.jsxs)("a",{href:a.url,className:`relative w-12 h-12 rounded-xl bg-gradient-to-r ${c} flex items-center justify-center hover:scale-110 transition-all duration-300 ${e} hover:shadow-lg group`,title:a.platform,style:{animationDelay:`${(b+5)*100}ms`},children:[(0,d.jsx)("i",{className:`${a.icon} text-white text-lg group-hover:scale-110 transition-transform duration-300`}),(0,d.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-xl scale-0 group-hover:scale-100 transition-transform duration-300"})]},b)})})]})]})})]})})}},6539:(a,b,c)=>{"use strict";c.d(b,{bv:()=>e,i3:()=>d});let d={baseURL:"",uploadsURL:""},e=a=>a?(a.startsWith("http://")||a.startsWith("https://"),a):null},6745:(a,b,c)=>{"use strict";c.d(b,{$L:()=>w,Cm:()=>k,It:()=>j,KT:()=>i,OX:()=>p,WH:()=>m,aU:()=>h,bW:()=>q,dt:()=>u,ro:()=>s,sB:()=>o,t1:()=>v,ub:()=>r,wf:()=>t,y9:()=>l,yI:()=>n,zm:()=>g});let d=c(6539).i3.baseURL,e=async(a,b={})=>{try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(b){throw console.error(`API call failed for ${a}:`,b),b}},f=(a=500)=>new Promise(b=>setTimeout(b,a)),g=async()=>{try{return await f(),await e("/api/hero")}catch(a){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",a),a}},h=async(a,b=null)=>{try{await f();let b=await e("/api/hero"),c=b?.id||1;return await e(`/api/hero/${c}`,{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",a),a}},i=async()=>{try{return await f(),await e("/api/kitchens")}catch(a){throw console.error("خطأ في جلب بيانات المطابخ:",a),a}},j=async(a,b=null)=>{try{return await f(),await e("/api/kitchens",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة مطبخ:",a),a}},k=async(a,b,c=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث مطبخ:",a),a}},l=async(a,b=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف مطبخ:",a),a}},m=async()=>{try{return await f(),await e("/api/cabinets")}catch(a){throw console.error("خطأ في جلب بيانات الخزائن:",a),a}},n=async(a,b=null)=>{try{return await f(),await e("/api/cabinets",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة خزانة:",a),a}},o=async(a,b,c=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث خزانة:",a),a}},p=async(a,b=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف خزانة:",a),a}},q=async()=>{try{return await f(),await e("/api/categories")}catch(a){throw console.error("خطأ في جلب الفئات:",a),a}},r=async()=>{try{return await f(),await e("/api/why-choose-us")}catch(a){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",a),a}},s=async(a,b=null)=>{try{return await f(),await e("/api/why-choose-us",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",a),a}},t=async()=>{try{return await f(),await e("/api/footer")}catch(a){throw console.error("خطأ في جلب بيانات التذييل:",a),a}},u=async(a,b=null)=>{try{return await f(),await e("/api/footer",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات التذييل:",a),a}},v=async()=>{try{return await f(),await e("/api/company-settings")}catch(a){throw console.error("خطأ في جلب إعدادات الشركة:",a),a}},w=async(a,b,c=null)=>{try{return await f(),{success:!0}}catch(a){throw console.error("خطأ في تحديث إعدادات الشركة:",a),a}}},7522:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8732);function e({Component:a,pageProps:b}){return(0,d.jsx)("div",{dir:"rtl",children:(0,d.jsx)(a,{...b})})}c(8754),c(2015),c(8015)},8015:(a,b,c)=>{"use strict";c.d(b,{aB:()=>f,wU:()=>e});var d=c(6745);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,d.zm)(),(0,d.ub)(),(0,d.KT)(),(0,d.WH)(),(0,d.wf)(),(0,d.bW)(),(0,d.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(a){return console.error("❌ Failed to initialize database connection:",a),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},f=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(a){return console.error("❌ Failed to force reset database:",a),!1}}},8754:()=>{},9674:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(8732),e=c(2341);function f(){return(0,d.jsxs)(e.Html,{lang:"ar",dir:"rtl",children:[(0,d.jsxs)(e.Head,{children:[(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",href:"/icon.svg"}),(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",sizes:"16x16",href:"/favicon-16x16.svg"}),(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",sizes:"32x32",href:"/favicon-32x32.svg"}),(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.svg"}),(0,d.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"true"}),(0,d.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap",rel:"stylesheet"}),(0,d.jsx)("link",{href:"https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css",rel:"stylesheet"}),(0,d.jsx)("meta",{name:"geo.region",content:"SA"}),(0,d.jsx)("meta",{name:"geo.placename",content:"الرياض"}),(0,d.jsx)("meta",{name:"format-detection",content:"telephone=yes"}),(0,d.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://cdnjs.cloudflare.com"})]}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.Main,{}),(0,d.jsx)(e.NextScript,{})]})]})}}};