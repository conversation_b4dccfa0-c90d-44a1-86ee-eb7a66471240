(()=>{var a={};a.id=731,a.ids=[220,636,731],a.modules={156:a=>{"use strict";a.exports=require("next/dist/shared/lib/utils")},361:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1322:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/format-url")},2015:a=>{"use strict";a.exports=require("react")},3103:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},3873:a=>{"use strict";a.exports=require("path")},5379:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return k}});let d=c(7020),e=c(8732),f=d._(c(2015)),g=d._(c(1145)),h={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function i(a){let b,{req:d,res:e,err:f}=a,g=e&&e.statusCode?e.statusCode:f?f.statusCode:404;if(d){let{getRequestMeta:a}=c(5124),e=a(d,"initURL");e&&(b=new URL(e).hostname)}return{statusCode:g,hostname:b}}let j={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class k extends f.default.Component{render(){let{statusCode:a,withDarkMode:b=!0}=this.props,c=this.props.title||h[a]||"An unexpected error has occurred";return(0,e.jsxs)("div",{style:j.error,children:[(0,e.jsx)(g.default,{children:(0,e.jsx)("title",{children:a?a+": "+c:"Application error: a client-side exception has occurred"})}),(0,e.jsxs)("div",{style:j.desc,children:[(0,e.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(b?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),a?(0,e.jsx)("h1",{className:"next-error-h1",style:j.h1,children:a}):null,(0,e.jsx)("div",{style:j.wrap,children:(0,e.jsxs)("h2",{style:j.h2,children:[this.props.title||a?c:(0,e.jsxs)(e.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,e.jsxs)(e.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}k.displayName="ErrorPage",k.getInitialProps=i,k.origGetInitialProps=i,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5952:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6539:(a,b,c)=>{"use strict";c.d(b,{bv:()=>e,i3:()=>d});let d={baseURL:"",uploadsURL:""},e=a=>a?(a.startsWith("http://")||a.startsWith("https://"),a):null},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6745:(a,b,c)=>{"use strict";c.d(b,{$L:()=>w,Cm:()=>k,It:()=>j,KT:()=>i,OX:()=>p,WH:()=>m,aU:()=>h,bW:()=>q,dt:()=>u,ro:()=>s,sB:()=>o,t1:()=>v,ub:()=>r,wf:()=>t,y9:()=>l,yI:()=>n,zm:()=>g});let d=c(6539).i3.baseURL,e=async(a,b={})=>{try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(b){throw console.error(`API call failed for ${a}:`,b),b}},f=(a=500)=>new Promise(b=>setTimeout(b,a)),g=async()=>{try{return await f(),await e("/api/hero")}catch(a){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",a),a}},h=async(a,b=null)=>{try{await f();let b=await e("/api/hero"),c=b?.id||1;return await e(`/api/hero/${c}`,{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",a),a}},i=async()=>{try{return await f(),await e("/api/kitchens")}catch(a){throw console.error("خطأ في جلب بيانات المطابخ:",a),a}},j=async(a,b=null)=>{try{return await f(),await e("/api/kitchens",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة مطبخ:",a),a}},k=async(a,b,c=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث مطبخ:",a),a}},l=async(a,b=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف مطبخ:",a),a}},m=async()=>{try{return await f(),await e("/api/cabinets")}catch(a){throw console.error("خطأ في جلب بيانات الخزائن:",a),a}},n=async(a,b=null)=>{try{return await f(),await e("/api/cabinets",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة خزانة:",a),a}},o=async(a,b,c=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث خزانة:",a),a}},p=async(a,b=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف خزانة:",a),a}},q=async()=>{try{return await f(),await e("/api/categories")}catch(a){throw console.error("خطأ في جلب الفئات:",a),a}},r=async()=>{try{return await f(),await e("/api/why-choose-us")}catch(a){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",a),a}},s=async(a,b=null)=>{try{return await f(),await e("/api/why-choose-us",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",a),a}},t=async()=>{try{return await f(),await e("/api/footer")}catch(a){throw console.error("خطأ في جلب بيانات التذييل:",a),a}},u=async(a,b=null)=>{try{return await f(),await e("/api/footer",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات التذييل:",a),a}},v=async()=>{try{return await f(),await e("/api/company-settings")}catch(a){throw console.error("خطأ في جلب إعدادات الشركة:",a),a}},w=async(a,b,c=null)=>{try{return await f(),{success:!0}}catch(a){throw console.error("خطأ في تحديث إعدادات الشركة:",a),a}}},7063:(a,b,c)=>{"use strict";c.r(b),c.d(b,{config:()=>I,default:()=>E,getServerSideProps:()=>H,getStaticPaths:()=>G,getStaticProps:()=>F,handler:()=>Q,reportWebVitals:()=>J,routeModule:()=>P,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>O,unstable_getStaticParams:()=>M,unstable_getStaticPaths:()=>L,unstable_getStaticProps:()=>K});var d=c(3885),e=c(237),f=c(772),g=c(2410),h=c(1322),i=c(5124),j=c(8647),k=c(3709),l=c(7909),m=c(5122),n=c(1413),o=c(9674),p=c(7522),q=c(5379),r=c.n(q),s=c(6755),t=c(156),u=c(12),v=c(2072),w=c(8164),x=c(4971),y=c(8737),z=c(6439),A=c(5735),B=c(6713),C=c(3103),D=c(5952);let E=(0,n.M)(q,"default"),F=(0,n.M)(q,"getStaticProps"),G=(0,n.M)(q,"getStaticPaths"),H=(0,n.M)(q,"getServerSideProps"),I=(0,n.M)(q,"config"),J=(0,n.M)(q,"reportWebVitals"),K=(0,n.M)(q,"unstable_getStaticProps"),L=(0,n.M)(q,"unstable_getStaticPaths"),M=(0,n.M)(q,"unstable_getStaticParams"),N=(0,n.M)(q,"unstable_getServerProps"),O=(0,n.M)(q,"unstable_getServerSideProps"),P=new d.PagesRouteModule({definition:{kind:e.RouteKind.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:p.default,Document:o.default},userland:q});async function Q(a,b,c){var d,n;let o="/_error";"/index"===o&&(o="/");let p="false",E=await P.prepare(a,b,{srcPage:o,multiZoneDraftMode:p});if(!E){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:J,query:K,params:L,parsedUrl:M,originalQuery:N,originalPathname:O,buildManifest:Q,nextFontManifest:R,serverFilesManifest:S,reactLoadableManifest:T,prerenderManifest:U,isDraftMode:V,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,locale:Y,locales:Z,defaultLocale:$,routerServerContext:_,nextConfig:aa,resolvedPathname:ab}=E,ac=null==S||null==(n=S.config)||null==(d=n.experimental)?void 0:d.isExperimentalCompile,ad=!!H,ae=!!F,af=!!G,ag=!!(r()||q).getInitialProps,ah=K.amp&&I.amp,ai=null,aj=!1,ak=E.isNextDataRequest&&(ae||ad),al="/404"===o,am="/500"===o,an="/_error"===o;if(P.isDev||V||!ae||(ai=`${Y?`/${Y}`:""}${("/"===o||"/"===ab)&&Y?"":ab}${ah?".amp":""}`,(al||am||an)&&(ai=`${Y?`/${Y}`:""}${o}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!V){let a=(0,D.removeTrailingSlash)(Y?(0,C.addPathPrefix)(ab,`/${Y}`):ab),b=!!U.routes[a]||U.notFoundRoutes.includes(a),c=U.dynamicRoutes[o];if(c){if(!1===c.fallback&&!b)throw new z.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,B.isBot)(a.headers["user-agent"]||"")||(0,i.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,g.getTracer)(),ap=ao.getActiveScopeSpan();try{let d=a.method||"GET",n=(0,h.formatUrl)({pathname:aa.trailingSlash?M.pathname:(0,D.removeTrailingSlash)(M.pathname||"/"),query:ae?{}:N}),r=(null==_?void 0:_.publicRuntimeConfig)||aa.publicRuntimeConfig,z=async g=>{var z,B;let C,D=async({previousCacheEntry:s})=>{var t;let u=async()=>{try{var c,e,t;return await P.render(a,b,{query:ae&&!ac?{...L,...ah?{amp:K.amp}:{}}:{...K,...L},params:L,page:o,renderContext:{isDraftMode:V,isFallback:aj,developmentNotFoundSourcePage:(0,i.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:J,customServer:!!(null==_?void 0:_.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:L,routeModule:P,page:o,pageConfig:I||{},Component:(0,j.T)(q),ComponentMod:q,getStaticProps:F,getStaticPaths:G,getServerSideProps:H,supportsDynamicResponse:!ae,buildManifest:Q,nextFontManifest:R,reactLoadableManifest:T,assetPrefix:aa.assetPrefix,strictNextHead:!!aa.experimental.strictNextHead,previewProps:U.preview,images:aa.images,nextConfigOutput:aa.output,optimizeCss:!!aa.experimental.optimizeCss,nextScriptWorkers:!!aa.experimental.nextScriptWorkers,domainLocales:null==(c=aa.i18n)?void 0:c.domains,crossOrigin:aa.crossOrigin,multiZoneDraftMode:p,basePath:aa.basePath,canonicalBase:aa.amp.canonicalBase||"",ampOptimizerConfig:null==(e=aa.experimental.amp)?void 0:e.optimizer,disableOptimizedLoading:aa.experimental.disableOptimizedLoading,largePageDataBytes:aa.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ac,experimental:{clientTraceMetadata:aa.experimental.clientTraceMetadata||[]},locale:Y,locales:Z,defaultLocale:$,setIsrStatus:null==_?void 0:_.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:n,resolvedAsPath:ad||ag?(0,h.formatUrl)({pathname:ak?(0,l.normalizeDataPath)(O):O,query:N}):n,isOnDemandRevalidate:W,ErrorDebug:(0,i.getRequestMeta)(a,"PagesErrorDebug"),err:(0,i.getRequestMeta)(a,"invokeError"),dev:P.isDev,distDir:`${P.projectDir}/${P.distDir}`,ampSkipValidation:null==(t=aa.experimental.amp)?void 0:t.skipValidation,ampValidator:(0,i.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:m.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:m.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!g)return;g.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==f.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=c.get("next.route");if(e){let a=`${d} ${e}`;g.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),g.updateName(a)}else g.updateName(`${d} ${a.url}`)})}catch(b){throw(null==s?void 0:s.isStale)&&await P.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,k.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}};if(s&&(aj=!1),aj){let b=await P.getResponseCache(a).get(P.isDev?null:Y?`/${Y}${o}`:o,async({previousCacheEntry:a=null})=>P.isDev?u():(0,y.toResponseCacheEntry)(a),{routeKind:e.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await P.getIncrementalCache(a,aa,U),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,i.getRequestMeta)(a,"minimalMode")&&W&&X&&!s?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==s||null==(t=s.value)?void 0:t.kind)===m.CachedRouteKind.PAGES?{value:{kind:m.CachedRouteKind.PAGES,html:new x.default(Buffer.from(s.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:s.value.status,headers:s.value.headers}}),pageData:{},status:s.value.status,headers:s.value.headers},cacheControl:{revalidate:0,expire:void 0}}:u()},E=await P.handleResponse({cacheKey:ai,req:a,nextConfig:aa,routeKind:e.RouteKind.PAGES,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,waitUntil:c.waitUntil,responseGenerator:D,prerenderManifest:U});if(!aj||(null==E?void 0:E.isMiss)||(aj=!1),E){if(ae&&!(0,i.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",W?"REVALIDATED":E.isMiss?"MISS":E.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(C={revalidate:0,expire:void 0});else if(al){let b=(0,i.getRequestMeta)(a,"notFoundRevalidate");C={revalidate:void 0===b?0:b,expire:void 0}}else if(am)C={revalidate:0,expire:void 0};else if(E.cacheControl)if("number"==typeof E.cacheControl.revalidate){if(E.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${E.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});C={revalidate:E.cacheControl.revalidate,expire:(null==(z=E.cacheControl)?void 0:z.expire)??aa.expireTime}}else C={revalidate:v.CACHE_ONE_YEAR,expire:void 0};if(C&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,s.getCacheControlHeader)(C)),!E.value)return((0,i.addRequestMeta)(a,"notFoundRevalidate",null==(B=E.cacheControl)?void 0:B.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==_?void 0:_.render404)?await _.render404(a,b,M,!1):b.end("This page could not be found"));if(E.value.kind===m.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,u.getRedirectStatus)(c),{basePath:e}=aa;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,t.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===A.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(E.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(E.value.props));return}if(E.value.kind!==m.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(P.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),V&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,i.getRequestMeta)(a,"customErrorRender")||an&&(0,i.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,w.sendRenderResult)({req:a,res:b,result:!ak||an||am?E.value.html:new x.default(Buffer.from(JSON.stringify(E.value.pageData)),{contentType:"application/json",metadata:E.value.html.metadata}),generateEtags:aa.generateEtags,poweredByHeader:aa.poweredByHeader,cacheControl:P.isDev?void 0:C,type:ak?"json":"html"})}};ap?await z():await ao.withPropagatedContext(a.headers,()=>ao.trace(f.BaseServerSpan.handleRequest,{spanName:`${d} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":d,"http.target":a.url}},z))}catch(b){throw await P.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,k.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}}},7522:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8732);function e({Component:a,pageProps:b}){return(0,d.jsx)("div",{dir:"rtl",children:(0,d.jsx)(a,{...b})})}c(8754),c(2015),c(8015)},7909:a=>{"use strict";a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},8015:(a,b,c)=>{"use strict";c.d(b,{aB:()=>f,wU:()=>e});var d=c(6745);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,d.zm)(),(0,d.ub)(),(0,d.KT)(),(0,d.WH)(),(0,d.wf)(),(0,d.bW)(),(0,d.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(a){return console.error("❌ Failed to initialize database connection:",a),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},f=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(a){return console.error("❌ Failed to force reset database:",a),!1}}},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},8754:()=>{},9674:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(8732),e=c(2341);function f(){return(0,d.jsxs)(e.Html,{lang:"ar",dir:"rtl",children:[(0,d.jsxs)(e.Head,{children:[(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",href:"/icon.svg"}),(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",sizes:"16x16",href:"/favicon-16x16.svg"}),(0,d.jsx)("link",{rel:"icon",type:"image/svg+xml",sizes:"32x32",href:"/favicon-32x32.svg"}),(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.svg"}),(0,d.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"true"}),(0,d.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap",rel:"stylesheet"}),(0,d.jsx)("link",{href:"https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css",rel:"stylesheet"}),(0,d.jsx)("meta",{name:"geo.region",content:"SA"}),(0,d.jsx)("meta",{name:"geo.placename",content:"الرياض"}),(0,d.jsx)("meta",{name:"format-detection",content:"telephone=yes"}),(0,d.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,d.jsx)("link",{rel:"preconnect",href:"https://cdnjs.cloudflare.com"})]}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.Main,{}),(0,d.jsx)(e.NextScript,{})]})]})}}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[341,856],()=>b(b.s=7063));module.exports=c})();