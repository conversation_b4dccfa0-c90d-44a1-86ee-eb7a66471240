(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{2149:(t,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c});var o=a(7876);a(7790);var e=a(4232),n=a(5506);function c(t){let{Component:r,pageProps:a}=t;return(0,e.useEffect)(()=>{(0,n.wU)().catch(console.error)},[]),(0,o.jsx)("div",{dir:"rtl",children:(0,o.jsx)(r,{...a})})}},5506:(t,r,a)=>{"use strict";a.d(r,{aB:()=>n,wU:()=>e});var o=a(6686);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,o.zm)(),(0,o.ub)(),(0,o.KT)(),(0,o.WH)(),(0,o.wf)(),(0,o.bW)(),(0,o.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(t){return console.error("❌ Failed to initialize database connection:",t),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},n=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(t){return console.error("❌ Failed to force reset database:",t),!1}}},6556:(t,r,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return a(2149)}])},6686:(t,r,a)=>{"use strict";a.d(r,{$L:()=>P,Cm:()=>l,It:()=>h,KT:()=>s,OX:()=>f,WH:()=>u,aU:()=>i,bW:()=>p,dt:()=>m,ro:()=>b,sB:()=>d,t1:()=>T,ub:()=>g,wf:()=>v,y9:()=>w,yI:()=>y,zm:()=>c});let o=a(9478).i3.baseURL,e=async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=await fetch("".concat(o).concat(t),{headers:{"Content-Type":"application/json",...r.headers},...r});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));return await a.json()}catch(r){throw console.error("API call failed for ".concat(t,":"),r),r}},n=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:500;return new Promise(r=>setTimeout(r,t))},c=async()=>{try{return await n(),await e("/api/hero")}catch(t){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",t),t}},i=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{await n();let r=await e("/api/hero"),a=(null==r?void 0:r.id)||1;return await e("/api/hero/".concat(a),{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",t),t}},s=async()=>{try{return await n(),await e("/api/kitchens")}catch(t){throw console.error("خطأ في جلب بيانات المطابخ:",t),t}},h=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/kitchens",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة مطبخ:",t),t}},l=async function(t,r){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/api/kitchens/".concat(t),{method:"PUT",body:JSON.stringify(r)})}catch(t){throw console.error("خطأ في تحديث مطبخ:",t),t}},w=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/kitchens/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف مطبخ:",t),t}},u=async()=>{try{return await n(),await e("/api/cabinets")}catch(t){throw console.error("خطأ في جلب بيانات الخزائن:",t),t}},y=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/cabinets",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة خزانة:",t),t}},d=async function(t,r){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/api/cabinets/".concat(t),{method:"PUT",body:JSON.stringify(r)})}catch(t){throw console.error("خطأ في تحديث خزانة:",t),t}},f=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/cabinets/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف خزانة:",t),t}},p=async()=>{try{return await n(),await e("/api/categories")}catch(t){throw console.error("خطأ في جلب الفئات:",t),t}},g=async()=>{try{return await n(),await e("/api/why-choose-us")}catch(t){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",t),t}},b=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/why-choose-us",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",t),t}},v=async()=>{try{return await n(),await e("/api/footer")}catch(t){throw console.error("خطأ في جلب بيانات التذييل:",t),t}},m=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/footer",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات التذييل:",t),t}},T=async()=>{try{return await n(),await e("/api/company-settings")}catch(t){throw console.error("خطأ في جلب إعدادات الشركة:",t),t}},P=async function(t,r){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),{success:!0}}catch(t){throw console.error("خطأ في تحديث إعدادات الشركة:",t),t}}},7790:()=>{},9478:(t,r,a)=>{"use strict";a.d(r,{bv:()=>e,i3:()=>o});let o={baseURL:"",uploadsURL:""},e=t=>t?(t.startsWith("http://")||t.startsWith("https://"),t):null}},t=>{var r=r=>t(t.s=r);t.O(0,[593,792],()=>(r(6556),r(8253))),_N_E=t.O()}]);