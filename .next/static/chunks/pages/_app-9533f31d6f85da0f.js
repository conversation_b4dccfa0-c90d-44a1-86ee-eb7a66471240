(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{2149:(t,a,o)=>{"use strict";o.r(a),o.d(a,{default:()=>c});var r=o(7876);o(7790);var e=o(4232),n=o(5506);function c(t){let{Component:a,pageProps:o}=t;return(0,e.useEffect)(()=>{(0,n.wU)().catch(console.error)},[]),(0,r.jsx)("div",{dir:"rtl",children:(0,r.jsx)(a,{...o})})}},5506:(t,a,o)=>{"use strict";o.d(a,{aB:()=>n,wU:()=>e});var r=o(6686);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,r.zm)(),(0,r.ub)(),(0,r.KT)(),(0,r.WH)(),(0,r.wf)(),(0,r.bW)(),(0,r.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(t){return console.error("❌ Failed to initialize database connection:",t),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},n=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(t){return console.error("❌ Failed to force reset database:",t),!1}}},6556:(t,a,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return o(2149)}])},6686:(t,a,o)=>{"use strict";o.d(a,{$L:()=>P,Cm:()=>l,It:()=>h,KT:()=>s,OX:()=>f,WH:()=>u,aU:()=>i,bW:()=>p,dt:()=>m,ro:()=>g,sB:()=>d,t1:()=>T,ub:()=>b,wf:()=>v,y9:()=>w,yI:()=>y,zm:()=>c});let r=o(9478).i3.baseURL,e=async function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let o=await fetch("".concat(r).concat(t),{headers:{"Content-Type":"application/json",...a.headers},...a});if(!o.ok)throw Error("HTTP error! status: ".concat(o.status));return await o.json()}catch(a){throw console.error("API call failed for ".concat(t,":"),a),a}},n=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:500;return new Promise(a=>setTimeout(a,t))},c=async()=>{try{return await n(),await e("/api/hero")}catch(t){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",t),t}},i=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{await n();let a=await e("/api/hero"),o=(null==a?void 0:a.id)||1;return await e("/api/hero/".concat(o),{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",t),t}},s=async()=>{try{return await n(),await e("/api/kitchens")}catch(t){throw console.error("خطأ في جلب بيانات المطابخ:",t),t}},h=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/kitchens",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة مطبخ:",t),t}},l=async function(t,a){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/api/kitchens/".concat(t),{method:"PUT",body:JSON.stringify(a)})}catch(t){throw console.error("خطأ في تحديث مطبخ:",t),t}},w=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/kitchens/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف مطبخ:",t),t}},u=async()=>{try{return await n(),await e("/api/cabinets")}catch(t){throw console.error("خطأ في جلب بيانات الخزائن:",t),t}},y=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/cabinets",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة خزانة:",t),t}},d=async function(t,a){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/api/cabinets/".concat(t),{method:"PUT",body:JSON.stringify(a)})}catch(t){throw console.error("خطأ في تحديث خزانة:",t),t}},f=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/cabinets/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف خزانة:",t),t}},p=async()=>{try{return await n(),await e("/api/categories")}catch(t){throw console.error("خطأ في جلب الفئات:",t),t}},b=async()=>{try{return await n(),await e("/api/why-choose-us")}catch(t){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",t),t}},g=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/why-choose-us",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",t),t}},v=async()=>{try{return await n(),await e("/api/footer")}catch(t){throw console.error("خطأ في جلب بيانات التذييل:",t),t}},m=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/api/footer",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات التذييل:",t),t}},T=async()=>{try{return await n(),await e("/api/company-settings")}catch(t){throw console.error("خطأ في جلب إعدادات الشركة:",t),t}},P=async function(t,a){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),{success:!0}}catch(t){throw console.error("خطأ في تحديث إعدادات الشركة:",t),t}}},7790:()=>{},9478:(t,a,o)=>{"use strict";o.d(a,{bv:()=>e,i3:()=>r});let r="localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname?{baseURL:"http://localhost:3002",uploadsURL:""}:{baseURL:"/api",uploadsURL:""},e=t=>t?(t.startsWith("http://")||t.startsWith("https://"),t):null}},t=>{var a=a=>t(t.s=a);t.O(0,[593,792],()=>(a(6556),a(8253))),_N_E=t.O()}]);