[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37"}, {"size": 463, "mtime": 1752635557499, "results": "38", "hashOfConfig": "39"}, {"size": 1725, "mtime": 1752631575132, "results": "40", "hashOfConfig": "39"}, {"size": 3441, "mtime": 1752631724686, "results": "41", "hashOfConfig": "39"}, {"size": 1269, "mtime": 1752634460814, "results": "42", "hashOfConfig": "39"}, {"size": 477, "mtime": 1752634978610, "results": "43", "hashOfConfig": "39"}, {"size": 475, "mtime": 1752634943717, "results": "44", "hashOfConfig": "39"}, {"size": 645, "mtime": 1752634586689, "results": "45", "hashOfConfig": "39"}, {"size": 477, "mtime": 1752634960134, "results": "46", "hashOfConfig": "39"}, {"size": 482, "mtime": 1752634995310, "results": "47", "hashOfConfig": "39"}, {"size": 6368, "mtime": 1752631689240, "results": "48", "hashOfConfig": "39"}, {"size": 2347, "mtime": 1752631603324, "results": "49", "hashOfConfig": "39"}, {"size": 6684, "mtime": 1752631654636, "results": "50", "hashOfConfig": "39"}, {"size": 3566, "mtime": 1751444435516, "results": "51", "hashOfConfig": "39"}, {"size": 5952, "mtime": 1751443429426, "results": "52", "hashOfConfig": "39"}, {"size": 9709, "mtime": 1751561588128, "results": "53", "hashOfConfig": "39"}, {"size": 10324, "mtime": 1751443430306, "results": "54", "hashOfConfig": "39"}, {"size": 18409, "mtime": 1751686925506, "results": "55", "hashOfConfig": "39"}, {"size": 9439, "mtime": 1751596455083, "results": "56", "hashOfConfig": "39"}, {"size": 17788, "mtime": 1751601289625, "results": "57", "hashOfConfig": "39"}, {"size": 11914, "mtime": 1751667340164, "results": "58", "hashOfConfig": "39"}, {"size": 17940, "mtime": 1751686839673, "results": "59", "hashOfConfig": "39"}, {"size": 14750, "mtime": 1751443432969, "results": "60", "hashOfConfig": "39"}, {"size": 12975, "mtime": 1751601688079, "results": "61", "hashOfConfig": "39"}, {"size": 3828, "mtime": 1751478236235, "results": "62", "hashOfConfig": "39"}, {"size": 10376, "mtime": 1751606566385, "results": "63", "hashOfConfig": "39"}, {"size": 3715, "mtime": 1751682164034, "results": "64", "hashOfConfig": "39"}, {"size": 32342, "mtime": 1752632174918, "results": "65", "hashOfConfig": "39"}, {"size": 923, "mtime": 1751443427609, "results": "66", "hashOfConfig": "39"}, {"size": 6074, "mtime": 1752643067498, "results": "67", "hashOfConfig": "39"}, {"size": 4282, "mtime": 1752643007566, "results": "68", "hashOfConfig": "39"}, {"size": 35082, "mtime": 1752632275538, "results": "69", "hashOfConfig": "39"}, {"size": 16864, "mtime": 1752631886012, "results": "70", "hashOfConfig": "39"}, {"size": 5001, "mtime": 1752632019825, "results": "71", "hashOfConfig": "39"}, {"size": 3517, "mtime": 1751443429007, "results": "72", "hashOfConfig": "39"}, {"size": 9400, "mtime": 1751696106894, "results": "73", "hashOfConfig": "39"}, {"size": 1607, "mtime": 1752640228297, "results": "74", "hashOfConfig": "39"}, {"size": 5079, "mtime": 1752632642474, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["187", "188"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["189", "190"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["191", "192"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["193", "194"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["195"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["196", "197", "198", "199", "200"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["201"], [], "/var/www/html/src/components/HeroSection.jsx", ["202"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["203", "204", "205", "206", "207"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["208"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], {"ruleId": "209", "severity": 1, "message": "210", "line": 74, "column": 6, "nodeType": "211", "endLine": 74, "endColumn": 8, "suggestions": "212"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 119, "column": 21, "nodeType": "215", "endLine": 123, "endColumn": 23}, {"ruleId": "209", "severity": 1, "message": "216", "line": 78, "column": 6, "nodeType": "211", "endLine": 78, "endColumn": 8, "suggestions": "217"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 123, "column": 21, "nodeType": "215", "endLine": 127, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "214", "line": 285, "column": 15, "nodeType": "215", "endLine": 289, "endColumn": 17}, {"ruleId": "213", "severity": 1, "message": "214", "line": 318, "column": 23, "nodeType": "215", "endLine": 318, "endColumn": 101}, {"ruleId": "213", "severity": 1, "message": "214", "line": 277, "column": 15, "nodeType": "215", "endLine": 281, "endColumn": 17}, {"ruleId": "213", "severity": 1, "message": "214", "line": 310, "column": 23, "nodeType": "215", "endLine": 310, "endColumn": 101}, {"ruleId": "218", "severity": 1, "message": "219", "line": 138, "column": 1, "nodeType": "220", "endLine": 144, "endColumn": 2}, {"ruleId": "213", "severity": 1, "message": "214", "line": 275, "column": 23, "nodeType": "215", "endLine": 279, "endColumn": 25}, {"ruleId": "213", "severity": 1, "message": "214", "line": 389, "column": 25, "nodeType": "215", "endLine": 393, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "214", "line": 493, "column": 29, "nodeType": "215", "endLine": 497, "endColumn": 31}, {"ruleId": "213", "severity": 1, "message": "214", "line": 531, "column": 17, "nodeType": "215", "endLine": 535, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "214", "line": 626, "column": 27, "nodeType": "215", "endLine": 630, "endColumn": 29}, {"ruleId": "209", "severity": 1, "message": "221", "line": 56, "column": 6, "nodeType": "211", "endLine": 56, "endColumn": 8, "suggestions": "222"}, {"ruleId": "209", "severity": 1, "message": "223", "line": 47, "column": 6, "nodeType": "211", "endLine": 47, "endColumn": 8, "suggestions": "224"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 396, "column": 19, "nodeType": "215", "endLine": 400, "endColumn": 21}, {"ruleId": "213", "severity": 1, "message": "214", "line": 468, "column": 25, "nodeType": "215", "endLine": 472, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "214", "line": 572, "column": 29, "nodeType": "215", "endLine": 576, "endColumn": 31}, {"ruleId": "213", "severity": 1, "message": "214", "line": 610, "column": 17, "nodeType": "215", "endLine": 614, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "214", "line": 705, "column": 27, "nodeType": "215", "endLine": 709, "endColumn": 29}, {"ruleId": "209", "severity": 1, "message": "225", "line": 99, "column": 6, "nodeType": "211", "endLine": 99, "endColumn": 8, "suggestions": "226"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["227"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["228"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["229"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["230"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["231"], {"desc": "232", "fix": "233"}, {"desc": "234", "fix": "235"}, {"desc": "236", "fix": "237"}, {"desc": "238", "fix": "239"}, {"desc": "240", "fix": "241"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "242", "text": "243"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "244", "text": "245"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "246", "text": "247"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "248", "text": "249"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "250", "text": "251"}, [2866, 2868], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [2248, 2250], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3175, 3177], "[defaultWhyChooseUsData]"]