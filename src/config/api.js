// API Configuration
// إعدادات API للبيئات المختلفة

const getApiConfig = () => {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    // Server-side rendering - use localhost for internal API calls
    return {
      baseURL: 'http://localhost:3002',
      uploadsURL: ''
    };
  }

  // تحديد البيئة الحالية
  const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  if (isDevelopment) {
    return {
      baseURL: 'http://localhost:3002',
      uploadsURL: '' // روابط نسبية - الصور في public/uploads
    };
  } else {
    // للإنتاج - استخدام Next.js API routes التي تعيد التوجيه للخادم الداخلي
    return {
      baseURL: '/api',
      uploadsURL: '' // روابط نسبية - الصور في public/uploads
    };
  }
};

export const API_CONFIG = getApiConfig();

// دالة مساعدة لبناء URL كامل للصور
export const getImageURL = (imagePath) => {
  if (!imagePath) return null;

  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة
  // هذا يعمل مع HTTP و HTTPS تلقائياً
  return imagePath;
};

// دالة مساعدة لبناء URL للـ API
export const getApiURL = (endpoint) => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};
