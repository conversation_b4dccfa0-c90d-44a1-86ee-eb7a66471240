// API Configuration
// إعدادات API للبيئات المختلفة

const getApiConfig = () => {
  // في Next.js، نستخدم API routes دائماً
  // سواء في التطوير أو الإنتاج
  return {
    baseURL: '',
    uploadsURL: ''
  };
};

export const API_CONFIG = getApiConfig();

// دالة مساعدة لبناء URL كامل للصور
export const getImageURL = (imagePath) => {
  if (!imagePath) return null;

  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة
  // هذا يعمل مع HTTP و HTTPS تلقائياً
  return imagePath;
};

// دالة مساعدة لبناء URL للـ API
export const getApiURL = (endpoint) => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};
